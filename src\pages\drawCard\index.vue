<route lang="jsonc" type="page">
{
  "layout": "default",
  "style": {
    "navigationBarTitleText": "抽卡模拟器"
  }
}
</route>

<script lang="ts" setup>
import type { IProduct } from '@/api/drawCard'
import type { IPrize } from '@/api/turntable'
import { onMounted, reactive, ref } from 'vue'
import { drawCardsAPI } from '@/api/drawCard'
import TurntableComponent from './turntable.vue'

// 类型别名
type Product = IProduct
type SpinResult = IPrize

// 响应式数据
const isDrawing = ref(false)
const totalValue = ref(0)
const userId = ref<string | null>(null)
const cards = ref<Product[]>([])
const flippedCards = ref<Set<number>>(new Set())
const showValuePopups = ref<Set<number>>(new Set())
const showTurntable = ref(false)
const turntableSpins = ref(0)
const spinResult = ref('')
const showTestResult = ref(false)
const testResult = ref('')

// 总价值计数器相关
const totalValueCounter = ref(0)
const countUpAnimation = ref<number | null>(null)

// 消息提示
const customMessage = reactive({
  show: false,
  text: '',
  timer: null as number | null,
})

// API 请求函数
async function drawCards() {
  try {
    console.log('开始请求抽卡API，用户ID:', userId.value)
    const response = await drawCardsAPI(userId.value || '')
    console.log('抽卡API响应:', response)
    return response.data
  }
  catch (error) {
    console.error('抽卡API请求失败:', error)
    throw error
  }
}

// 工具函数
function sleep(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms))
}

function showMessage(message: string, duration = 3000) {
  if (customMessage.timer) {
    clearTimeout(customMessage.timer)
  }

  customMessage.text = message
  customMessage.show = true

  customMessage.timer = setTimeout(() => {
    customMessage.show = false
    customMessage.timer = null
  }, duration) as unknown as number
}

// 数字动画函数
function animateCounter(newValue: number) {
  if (countUpAnimation.value) {
    clearTimeout(countUpAnimation.value)
  }

  const startValue = totalValueCounter.value
  const duration = 1000 // 1秒动画
  const startTime = Date.now()

  const animate = () => {
    const elapsed = Date.now() - startTime
    const progress = Math.min(elapsed / duration, 1)

    totalValueCounter.value = Math.round(startValue + (newValue - startValue) * progress)

    if (progress < 1) {
      countUpAnimation.value = setTimeout(animate, 16) as unknown as number
    }
  }

  countUpAnimation.value = setTimeout(animate, 16) as unknown as number
}

// 格式化数字
function formatNumber(num: number): string {
  return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',')
}

// 格式化价格（处理字符串或数字）
function formatPrice(price: number | string): string {
  if (!price || price === '')
    return '0'
  const numPrice = typeof price === 'string' ? Number(price) : price
  return Number.isNaN(numPrice) ? '0' : formatNumber(numPrice)
}

// 显示和翻转卡片
async function displayAndFlipCards(cardList: Product[]) {
  cards.value = cardList
  flippedCards.value.clear()
  showValuePopups.value.clear()

  let runningTotal = 0

  // 逐个翻转卡片
  for (let i = 0; i < cardList.length; i++) {
    await sleep(200)
    flippedCards.value.add(i)

    // 处理avgPrice可能是字符串或数字的情况
    const avgPrice = cardList[i].avgPrice
    const cardValue = typeof avgPrice === 'string' && avgPrice !== ''
      ? Number(avgPrice)
      : typeof avgPrice === 'number'
        ? avgPrice
        : 0

    if (cardValue > 0) {
      runningTotal += cardValue
      animateCounter(runningTotal)

      // 显示价值弹窗
      showValuePopups.value.add(i)

      // 3秒后隐藏价值弹窗
      setTimeout(() => {
        showValuePopups.value.delete(i)
      }, 3000)
    }
  }

  totalValue.value = runningTotal
}

// 处理抽卡
async function handleDraw() {
  if (isDrawing.value)
    return

  isDrawing.value = true
  cards.value = []
  flippedCards.value.clear()
  showValuePopups.value.clear()
  totalValueCounter.value = 0
  totalValue.value = 0
  showTurntable.value = false
  spinResult.value = ''

  try {
    const data = await drawCards()
    console.log(data)

    if (data.alreadyDrawn) {
      showMessage('您已经抽过卡了，为您显示上次的结果', 3000)
    }

    if (data.cards && data.cards.length > 0) {
      await displayAndFlipCards(data.cards)
    }

    // 处理转盘
    const agentCardsDrawn = data.turntableSpins || 0
    if (data.alreadyDrawn || agentCardsDrawn > 0) {
      showTurntable.value = true
      turntableSpins.value = agentCardsDrawn

      if (!data.alreadyDrawn && agentCardsDrawn > 0) {
        showMessage(`恭喜！获得 ${agentCardsDrawn} 次转盘机会！`)
      }
    }
  }
  catch (error) {
    console.error('Draw failed:', error)
    showMessage(`抽奖失败: ${error}`, 5000)
  }

  isDrawing.value = false
}

// 处理转盘结果
function handleSpinResult(prize: SpinResult) {
  if (prize && prize.value > 0) {
    totalValue.value += prize.value
    animateCounter(totalValue.value)

    spinResult.value = `恭喜！转盘抽中 ${prize.name} 哈弗币！`
  }
  else {
    spinResult.value = ''
  }
}

// 处理初始转盘价值
function handleInitialSpinsValue(value: number) {
  if (value > 0) {
    totalValue.value += value
    animateCounter(totalValue.value)
  }
}

// 初始化
onMounted(() => {
  try {
    // 获取URL参数中的用户ID
    const pages = getCurrentPages()
    const currentPage = pages[pages.length - 1]
    const options = (currentPage as any).options || {}
    userId.value = options.id || 'test'

    console.log('页面初始化，用户ID:', userId.value)
    console.log('页面参数:', options)

    if (!userId.value) {
      showMessage('未查询到此订单', 10000)
    }
  }
  catch (error) {
    console.error('页面初始化失败:', error)
    showMessage('页面初始化失败', 5000)
    userId.value = 'test' // 设置默认值
  }
})
</script>

<template>
  <view class="draw-card-container">
    <!-- 自定义消息提示 -->
    <view
      v-if="customMessage.show"
      class="custom-message"
      :class="{ 'is-visible': customMessage.show }"
    >
      {{ customMessage.text }}
    </view>

    <!-- 头部 -->
    <view class="header">
      <view class="header-content">
        <view class="header-left">
          <!-- <image src="" class="logo" mode="aspectFit" /> -->
          <view class="header-text">
            <text class="title">
              Mon电竞
            </text>
            <text class="subtitle">
              礼拜一电竞
            </text>
          </view>
        </view>
      </view>
    </view>

    <!-- 总价值显示 -->
    <view class="total-value">
      <view class="total-value-content">
        <view class="total-value-label">
          总价值:
        </view>
        <view class="total-value-counter">
          {{ formatNumber(totalValueCounter) }}
        </view>
        <view class="total-value-unit">
          哈弗币
        </view>
      </view>
    </view>

    <!-- 抽卡按钮 -->
    <view class="draw-section">
      <button
        class="draw-button"
        :class="{ disabled: isDrawing }"
        :disabled="isDrawing"
        @click="handleDraw"
      >
        {{ isDrawing ? '正在抽取...' : '抽取25张卡' }}
      </button>
    </view>

    <!-- 卡片展示区域 -->
    <view class="cards-container">
      <view
        v-for="(card, index) in cards"
        :key="index"
        class="product-card"
        :class="[card.theme, { 'is-flipped': flippedCards.has(index) }]"
      >
        <!-- 价值弹窗 -->
        <view class="card-value-popup" :class="{ show: showValuePopups.has(index) }">
          +{{ formatPrice(card.avgPrice) }}
        </view>

        <!-- 卡片正面 -->
        <view class="product-card-front">
          <view class="back-header">
            <view class="back-title-en">
              DELTA SQUAD
            </view>
            <view class="back-title-cn">
              摸金卡
            </view>
          </view>
          <view class="back-logo">
            <text class="logo-text">
              LOGO
            </text>
          </view>
          <view class="back-footer">
            SHAN JIAO ZHO
          </view>
        </view>

        <!-- 卡片背面 -->
        <view class="product-card-back">
          <view class="product-header">
            <view class="product-name-label">
              名称/NAME
            </view>
            <view class="product-name-cn">
              {{ card.objectName }}
            </view>
            <view class="product-name-en">
              DELTA SQUAD
            </view>
          </view>

          <view class="product-image-container">
            <image :src="card.prePic" :alt="card.objectName" class="product-image" mode="aspectFit" />
            <view class="brand-logo">
              DELTA SQUAD
            </view>
          </view>

          <view class="product-footer">
            <view class="product-price-info">
              <view class="product-avgPrice-label">
                哈弗币价值/avgPrice
              </view>
              <view class="product-avgPrice">
                {{ card.avgPrice ? formatPrice(card.avgPrice) : '--' }}
              </view>
              <view class="product-weight">
                {{ card.weight ? `${card.weight} kg` : '-' }}
              </view>
            </view>
            <view class="grid-icon">
              <view
                v-for="cellIndex in 16"
                :key="cellIndex"
                class="grid-cell"
                :class="{
                  highlight: Math.floor((cellIndex - 1) / 4) < card.width && ((cellIndex - 1) % 4) < card.length,
                }"
              />
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 转盘区域 -->
    <view v-if="showTurntable" class="turntable-section">
      <TurntableComponent
        :user-id="userId"
        :initial-spins="turntableSpins"
        @spin-result="handleSpinResult"
        @initial-spins-value="handleInitialSpinsValue"
      />
      <view class="spin-result">
        {{ spinResult }}
      </view>
    </view>

    <!-- 测试结果区域 -->
    <view v-if="showTestResult" class="test-result-section">
      <text class="test-result-title">
        测试结果
      </text>
      <text class="test-result-output">
        {{ testResult }}
      </text>
    </view>
  </view>
</template>

<style lang="scss" scoped>
// 根容器样式
.draw-card-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #0f172a 0%, #1e1b4b 100%);
  color: #e2e8f0;
  font-family: 'Noto Sans SC', 'Montserrat', sans-serif;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
      radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.04) 1px, transparent 1px),
      radial-gradient(circle at 75% 75%, rgba(255, 255, 255, 0.04) 1px, transparent 1px);
    background-size: 50px 50px;
    pointer-events: none;
    z-index: 0;
  }
}

// 自定义消息样式
.custom-message {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: linear-gradient(to right, rgba(30, 41, 59, 0.9), rgba(109, 40, 217, 0.8));
  border: 1px solid rgba(96, 165, 250, 0.3);
  color: #e2e8f0;
  padding: 15px 20px;
  border-radius: 8px;
  font-size: 16px;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow:
    0 8px 20px rgba(0, 0, 0, 0.4),
    0 0 30px rgba(96, 165, 250, 0.3);
  transition: opacity 0.4s ease-out;
  opacity: 0;
  white-space: nowrap;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);

  &.is-visible {
    opacity: 1;
  }
}

// 头部样式
.header {
  background: linear-gradient(to right, rgba(0, 0, 0, 0.9), rgba(0, 0, 0, 0.7));
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
  position: relative;
  overflow: hidden;
  z-index: 10;
  width: 100%;
  padding: 16px 0;

  &::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, transparent, #60a5fa, transparent);
    opacity: 0.8;
    z-index: 5;
  }
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
}

.header-left {
  display: flex;
  align-items: center;
}

.logo {
  height: 40px;
  width: 48px;
  margin-right: 12px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.header-text {
  display: flex;
  flex-direction: column;
}

.title {
  font-size: 20px;
  font-weight: bold;
  color: white;
}

.subtitle {
  font-size: 12px;
  color: #60a5fa;
}

// 总价值显示样式
.total-value {
  position: sticky;
  top: 0;
  width: 100%;
  background: rgba(15, 23, 42, 0.9);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
  z-index: 50;
  padding: 12px 0;
  text-align: center;
}

.total-value-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.total-value-label,
.total-value-unit {
  font-family: 'Audiowide', cursive;
  font-size: 18px;
  color: #e2e8f0;
}

.total-value-counter {
  font-family: 'Orbitron', monospace;
  font-weight: 700;
  font-size: 20px;
  color: #ff4d4d;
  text-shadow: 0 0 10px rgba(239, 68, 68, 0.8);
  letter-spacing: 1px;
  /* #ifndef MP-WEIXIN */
  background: linear-gradient(90deg, #ff4d4d, #f87171);
  -webkit-background-clip: text;
  background-clip: text;
  /* #endif */
  animation: pulse 2s infinite;
  min-width: 120px;
}

@keyframes pulse {
  0% {
    text-shadow: 0 0 10px rgba(239, 68, 68, 0.5);
  }
  50% {
    text-shadow: 0 0 20px rgba(239, 68, 68, 0.8);
  }
  100% {
    text-shadow: 0 0 10px rgba(239, 68, 68, 0.5);
  }
}

// 抽卡按钮样式
.draw-section {
  padding: 32px 0;
  text-align: center;
  position: relative;
  z-index: 1;
}

.draw-button {
  background: linear-gradient(135deg, #8b5cf6 0%, #6d28d9 100%);
  border: none;
  position: relative;
  overflow: hidden;
  transition:
    all 0.3s ease,
    transform 0.2s ease;
  box-shadow: 0 8px 16px rgba(109, 40, 217, 0.3);
  padding: 16px 32px;
  font-size: 18px;
  font-weight: bold;
  color: white;
  border-radius: 50px;
  cursor: pointer;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.7s ease;
  }

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(109, 40, 217, 0.4);

    &::before {
      left: 100%;
    }
  }

  &.disabled,
  &:disabled {
    background: linear-gradient(135deg, #9ca3af 0%, #6b7280 100%);
    transform: none;
    box-shadow: 0 4px 8px rgba(107, 114, 128, 0.3);
    cursor: not-allowed;

    &::before {
      display: none;
    }
  }
}

// 卡片容器样式
.cards-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
  gap: 16px 16px;
  padding: 24px;
  max-width: 100%;
  position: relative;
  z-index: 1;
  justify-items: center;
}

// 产品卡片样式
.product-card {
  position: relative;
  height: 220px;
  width: 160px;
  max-width: 100%;
  overflow: visible;
  border-radius: 15px;
  perspective: 1000px;
  background-color: transparent;
  margin-bottom: 16px;
  border-width: 5px;
  border-style: solid;
  transform-style: preserve-3d;
  transition: transform 0.8s;
}

.product-card.is-flipped {
  transform: rotateY(180deg);
}

.product-card-front,
.product-card-back {
  position: absolute;
  width: 100%;
  height: 100%;
  backface-visibility: hidden;
  overflow: hidden;
  border-radius: 10px;
  background: repeating-linear-gradient(-45deg, #4a4a4a, #4a4a4a 10px, #3a3a3a 10px, #3a3a3a 20px);
  color: white;
  padding: 10px;
  box-sizing: border-box;
}

.product-card-back {
  transform: rotateY(180deg);
}

.product-card-front {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
}

.back-header {
  text-align: center;
}

.back-title-en {
  font-size: 13px;
  color: #a4d8a4;
  font-weight: bold;
}

.back-title-cn {
  font-size: 16px;
  font-weight: bold;
  margin-top: 5px;
}

.back-logo {
  margin: 8px 0;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100px;
  height: 80px;
}

.logo-text {
  color: white;
  font-size: 14px;
  font-weight: bold;
}

.back-footer {
  font-size: 13px;
  font-weight: bold;
}

.product-card-back {
  display: flex;
  flex-direction: column;
}

.product-header {
  margin-bottom: 6px;
  flex-shrink: 0;
}

.product-name-label {
  font-size: 10px;
  opacity: 0.7;
  line-height: 1.2;
}

.product-name-cn {
  font-size: 14px;
  font-weight: bold;
  margin: 3px 0;
  line-height: 1.3;
  word-wrap: break-word;
  overflow-wrap: break-word;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  max-height: 2.6em;
}

.product-name-en {
  font-size: 8px;
  color: #3fafa9;
  font-weight: bold;
  line-height: 1.2;
}

.product-image-container {
  position: relative;
  min-height: 80px;
  display: flex;
  justify-content: center;
  align-items: center;
  flex: 1;
  margin: 4px 0;
}

.product-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

.brand-logo {
  position: absolute;
  bottom: 5px;
  right: 5px;
  font-size: 4px;
  opacity: 0.7;
}

.product-footer {
  position: relative;
  margin-top: auto;
  flex-shrink: 0;
  padding-top: 4px;
  width: 100%;
}

.product-price-info {
  text-align: right;
  width: 100%;
  padding-right: 24px;
}

.product-avgPrice-label {
  font-size: 10px;
  opacity: 0.7;
  line-height: 1.2;
  margin-bottom: 2px;
}

.product-avgPrice {
  font-size: 15px;
  font-weight: bold;
  line-height: 1.2;
  word-wrap: break-word;
}

.product-weight {
  font-size: 9px;
  margin-top: 2px;
  opacity: 0.8;
}

.grid-icon {
  position: absolute;
  bottom: 4px;
  left: 4px;
  width: 18px;
  height: 18px;
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  grid-gap: 1px;
}

.grid-cell {
  background-color: rgba(255, 255, 255, 0.2);
  height: 3px;

  &.highlight {
    background-color: white;
  }
}

// 卡片价值弹窗样式
.card-value-popup {
  position: absolute;
  top: -30px;
  left: 50%;
  transform: translateX(-50%);
  background-color: rgba(255, 215, 0, 0.8);
  color: #333;
  padding: 3px 8px;
  border-radius: 15px;
  font-weight: bold;
  opacity: 0;
  transition: all 0.5s;
  z-index: 10;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);

  &.show {
    opacity: 1;
    top: -40px;
  }
}

// 卡片主题样式
.theme-colorful {
  background: linear-gradient(45deg, red, orange, yellow, green, blue, indigo, violet);
  padding: 5px;
  border-radius: 15px;
  border: none;

  .product-image-container {
    background: repeating-linear-gradient(-45deg, #4a4a4a, #4a4a4a 10px, #3a3a3a 10px, #3a3a3a 20px);
  }
}

.theme-purple {
  border-color: #553486;

  .product-image-container {
    background: repeating-linear-gradient(-45deg, #3a3a6a, #3a3a6a 10px, #2a2a5a 10px, #2a2a5a 20px);
    border-color: #553486;
  }
}

.theme-red {
  border-color: #c00000;

  .product-image-container {
    background: repeating-linear-gradient(-45deg, #6e2a3a, #6e2a3a 10px, #5a1a2a 10px, #5a1a2a 20px);
    border-color: #a00000;
  }
}

.theme-yellow {
  border-color: #e8a833;

  .product-image-container {
    background: repeating-linear-gradient(-45deg, #d4ac0d, #d4ac0d 10px, #b8860b 10px, #b8860b 20px);
    border-color: #c89023;
  }
}

.theme-blue {
  border-color: #2c7fb8;

  .product-image-container {
    background: repeating-linear-gradient(-45deg, #3a6a8a, #3a6a8a 10px, #2a5a7a 10px, #2a5a7a 20px);
    border-color: #24689a;
  }
}

// 转盘区域样式
.turntable-section {
  position: relative;
  padding: 32px 0;
  margin-top: 48px;
  border-top: 1px solid rgba(255, 255, 255, 0.05);
  text-align: center;
  z-index: 1;
}

.spin-result {
  font-size: 18px;
  font-weight: 500;
  transition: all 0.3s ease;
  padding: 8px;
  text-align: center;
  margin-top: 24px;
  color: #28a745;
  min-height: 30px;
}

// 测试结果区域样式
.test-result-section {
  background: linear-gradient(to bottom right, rgba(30, 41, 59, 0.9), rgba(15, 23, 42, 0.9));
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(5px);
  transition: all 0.3s ease;
  overflow: hidden;
  margin: 20px auto;
  padding: 24px;
  width: 90%;
  max-width: 800px;
  position: relative;
  z-index: 1;
}

.test-result-title {
  font-size: 20px;
  font-weight: bold;
  margin-bottom: 16px;
  text-align: center;
  color: #e2e8f0;
  display: block;
}

.test-result-output {
  background: rgba(17, 24, 39, 0.7);
  border: 1px solid rgba(55, 65, 81, 0.5);
  border-radius: 8px;
  padding: 20px;
  transition: all 0.3s ease;
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
  color: #d1d5db;
  font-size: 14px;
  line-height: 1.5;
  white-space: pre-wrap;
  word-break: break-words;
  display: block;
}

// 响应式设计
@media (max-width: 768px) {
  .header-content {
    padding: 0 12px;
  }

  .logo {
    height: 32px;
    width: 40px;
    margin-right: 8px;
  }

  .title {
    font-size: 18px;
  }

  .subtitle {
    font-size: 10px;
  }

  .total-value-label,
  .total-value-unit {
    font-size: 16px;
  }

  .total-value-counter {
    font-size: 18px;
    min-width: 100px;
  }

  .draw-button {
    padding: 14px 28px;
    font-size: 16px;
  }

  .cards-container {
    padding: 16px;
    gap: 12px 12px;
    grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
  }

  .product-card {
    width: 140px;
    height: 200px;
    margin-bottom: 12px;
  }

  .product-card-back {
    padding: 8px;
  }

  .product-name-cn {
    font-size: 12px;
  }

  .product-image-container {
    min-height: 60px;
  }

  .product-avgPrice {
    font-size: 14px;
  }

  .product-price-info {
    padding-right: 20px;
  }

  .grid-icon {
    width: 16px;
    height: 16px;
  }

  .test-result-section {
    width: 95%;
    padding: 16px;
  }

  .test-result-output {
    padding: 12px;
    font-size: 12px;
  }
}

@media (max-width: 480px) {
  .cards-container {
    padding: 12px;
    gap: 10px 10px;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  }

  .product-card {
    width: 120px;
    height: 180px;
    margin-bottom: 10px;
  }

  .product-card-back {
    padding: 6px;
  }

  .product-name-cn {
    font-size: 11px;
    -webkit-line-clamp: 1;
    max-height: 1.3em;
  }

  .product-image-container {
    min-height: 50px;
  }

  .product-avgPrice {
    font-size: 13px;
  }

  .product-price-info {
    padding-right: 18px;
  }

  .grid-icon {
    width: 14px;
    height: 14px;
  }
}
</style>
